#!/usr/bin/env python3
"""
Script to test Manual Sync functionality directly
"""

import requests
import json
import time
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"

def test_manual_sync():
    """Test Manual Sync by calling the API directly"""
    print("🔍 Testing Manual Sync Functionality")
    print("=" * 50)
    
    # First, we need to get the integration ID
    try:
        # Get integrations (this will fail without auth, but let's try)
        response = requests.get(f"{API_BASE}/integrations/")
        if response.status_code == 200:
            integrations = response.json()
            print(f"✅ Found {len(integrations)} integrations")
            
            for integration in integrations:
                print(f"  - {integration['integration_type']}: {integration['name']}")
                
                # Test manual sync for this integration
                sync_response = requests.post(f"{API_BASE}/integrations/{integration['id']}/sync")
                if sync_response.status_code == 200:
                    result = sync_response.json()
                    print(f"  ✅ Manual sync result: {result['message']}")
                else:
                    print(f"  ❌ Manual sync failed: {sync_response.status_code}")
                    
        else:
            print(f"❌ Failed to get integrations: {response.status_code}")
            if response.status_code == 401:
                print("   Authentication required")
            elif response.status_code == 403:
                print("   Permission denied")
                
    except Exception as e:
        print(f"❌ Error testing manual sync: {e}")

def check_database_directly():
    """Check database directly using docker exec"""
    import subprocess
    
    print("\n🔍 Checking Database Directly")
    print("=" * 30)
    
    try:
        # Check integrations
        result = subprocess.run([
            "docker", "exec", "aggie-db-1", "psql", "-U", "postgres", "-d", "aggie_dev", 
            "-c", "SELECT id, integration_type, name, is_active, last_sync_status FROM invoice_integrations;"
        ], capture_output=True, text=True, check=True)
        
        print("📋 Integrations:")
        print(result.stdout)
        
        # Check recent AI agent sessions
        result = subprocess.run([
            "docker", "exec", "aggie-db-1", "psql", "-U", "postgres", "-d", "aggie_dev", 
            "-c", "SELECT COUNT(*) as sessions FROM agent_sessions WHERE created_at > NOW() - INTERVAL '1 hour';"
        ], capture_output=True, text=True, check=True)
        
        print("🤖 Recent AI Agent Sessions (last hour):")
        print(result.stdout)
        
        # Check recent invoices
        result = subprocess.run([
            "docker", "exec", "aggie-db-1", "psql", "-U", "postgres", "-d", "aggie_dev", 
            "-c", "SELECT COUNT(*) as invoices FROM invoices WHERE created_at > NOW() - INTERVAL '1 hour';"
        ], capture_output=True, text=True, check=True)
        
        print("📄 Recent Invoices (last hour):")
        print(result.stdout)
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Database check failed: {e}")
    except Exception as e:
        print(f"❌ Error checking database: {e}")

def monitor_logs():
    """Monitor logs for Manual Sync activity"""
    print("\n🔍 Recent Backend Logs")
    print("=" * 25)
    
    try:
        import subprocess
        
        # Get recent backend logs
        result = subprocess.run([
            "docker", "logs", "aggie-backend-api-1", "--tail", "10"
        ], capture_output=True, text=True, check=True)
        
        print("Backend API logs:")
        print(result.stdout)
        
        # Get recent worker logs
        result = subprocess.run([
            "docker", "logs", "aggie-worker-1", "--tail", "10"
        ], capture_output=True, text=True, check=True)
        
        print("\nCelery Worker logs:")
        print(result.stdout)
        
    except Exception as e:
        print(f"❌ Error getting logs: {e}")

def test_http_integration_directly():
    """Test the HTTP integration configuration directly"""
    print("\n🔍 Testing HTTP Integration Configuration")
    print("=" * 45)
    
    try:
        import subprocess
        
        # Get the HTTP integration configuration
        result = subprocess.run([
            "docker", "exec", "aggie-db-1", "psql", "-U", "postgres", "-d", "aggie_dev", 
            "-c", "SELECT id, name, configuration FROM invoice_integrations WHERE integration_type = 'HTTP';"
        ], capture_output=True, text=True, check=True)
        
        print("HTTP Integration Configuration:")
        print(result.stdout)
        
        # The configuration is encrypted, so we can't see the actual URL
        # But we can see if the integration exists and is configured
        
    except Exception as e:
        print(f"❌ Error checking HTTP integration: {e}")

def main():
    print("🚀 Manual Sync Diagnostic Tool")
    print("=" * 50)
    
    # Check database first
    check_database_directly()
    
    # Check HTTP integration
    test_http_integration_directly()
    
    # Check recent logs
    monitor_logs()
    
    # Try API test (will likely fail due to auth)
    test_manual_sync()
    
    print("\n" + "=" * 50)
    print("📋 SUMMARY:")
    print("1. Check if HTTP integration has a valid URL configured")
    print("2. Check if the URL is accessible from the Docker container")
    print("3. Check if Manual Sync is actually being called")
    print("4. Check if AI Agent tasks are being queued")
    
    print("\n💡 NEXT STEPS:")
    print("1. Go to frontend and try Manual Sync again")
    print("2. Watch the logs in real-time: docker logs aggie-backend-api-1 --follow")
    print("3. Check if any errors appear in the logs")

if __name__ == "__main__":
    main()
