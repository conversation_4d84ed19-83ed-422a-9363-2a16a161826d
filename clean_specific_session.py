#!/usr/bin/env python3
"""
Rensa specifik session
"""

import sys
import os
sys.path.append('apps/backend-api')

def clean_specific_session():
    """Rensa den specifika sessionen"""
    print('🧹 CLEANING SPECIFIC SESSION')
    print('=' * 50)
    
    try:
        # Use Docker database connection directly
        from sqlalchemy import create_engine, text
        from sqlalchemy.orm import sessionmaker
        
        docker_db_url = "postgresql://postgres:postgres@localhost:5432/aggie_dev"
        engine = create_engine(docker_db_url)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        
        db = SessionLocal()
        
        # Clean the specific session
        session_id = '257914ea-f467-418b-b334-b11f75f04727'
        
        clean_query = text("""
            UPDATE agent_sessions 
            SET status = 'FAILED',
                requires_human_review = true,
                action_item_reason = 'Session cleaned - testing fixes',
                completed_at = NOW()
            WHERE id = :session_id
            RETURNING id, status
        """)
        
        result = db.execute(clean_query, {"session_id": session_id})
        cleaned_session = result.fetchone()
        db.commit()
        
        if cleaned_session:
            print(f'✅ Cleaned session: {session_id}')
        else:
            print(f'❌ Session not found: {session_id}')
        
        # Also clean any running execution steps for this session
        clean_steps_query = text("""
            UPDATE execution_steps 
            SET status = 'FAILED',
                completed_at = NOW()
            WHERE session_id = :session_id
            AND status = 'RUNNING'
            RETURNING id, step_name
        """)
        
        result = db.execute(clean_steps_query, {"session_id": session_id})
        cleaned_steps = result.fetchall()
        db.commit()
        
        if cleaned_steps:
            print(f'✅ Cleaned {len(cleaned_steps)} running steps')
            for step in cleaned_steps:
                print(f'  - {step[1]}')
        
        db.close()
        return True
        
    except Exception as e:
        print(f'❌ Error cleaning session: {e}')
        return False

def main():
    clean_specific_session()

if __name__ == "__main__":
    main()
