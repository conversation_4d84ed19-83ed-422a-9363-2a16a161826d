#!/usr/bin/env python3
"""
Script to check database directly for AI Agent sessions and invoices
"""

import os
import sys
from datetime import datetime, timedelta
import sqlalchemy as sa
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Database connection
DATABASE_URL = "postgresql://postgres:postgres@localhost:5432/aggie_dev"
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def check_recent_activity():
    """Check recent AI Agent and invoice activity"""
    db = SessionLocal()
    try:
        print("🔍 Checking Database Status")
        print("=" * 50)

        # Check recent AI Agent sessions (last 24 hours)
        cutoff_time = datetime.utcnow() - timedelta(hours=24)

        # Use raw SQL to avoid model import issues
        sessions_query = text("""
            SELECT id, session_name, status, created_at, tenant_id,
                   invoice_unique_id, action_item_reason
            FROM agent_sessions
            WHERE created_at >= :cutoff_time
            ORDER BY created_at DESC
            LIMIT 20
        """)

        sessions_result = db.execute(sessions_query, {"cutoff_time": cutoff_time})
        recent_sessions = sessions_result.fetchall()

        print(f"\n📋 Recent AI Agent Sessions (last 24h): {len(recent_sessions)}")

        if recent_sessions:
            for session in recent_sessions:
                status_emoji = {
                    'pending': '⏳',
                    'running': '🔄',
                    'completed': '✅',
                    'failed': '❌',
                    'cancelled': '🚫'
                }.get(session.status, '❓')

                print(f"  {status_emoji} {session.session_name}")
                print(f"     Status: {session.status}")
                print(f"     Created: {session.created_at}")
                print(f"     Tenant: {session.tenant_id}")
                if session.invoice_unique_id:
                    print(f"     Invoice ID: {session.invoice_unique_id}")
                if session.action_item_reason:
                    print(f"     Action Item: {session.action_item_reason}")
                print()

        # Check recent invoices
        invoices_query = text("""
            SELECT id, supplier_name, status, total_amount, currency,
                   created_at, tenant_id, processing_error
            FROM invoices
            WHERE created_at >= :cutoff_time
            ORDER BY created_at DESC
            LIMIT 20
        """)

        invoices_result = db.execute(invoices_query, {"cutoff_time": cutoff_time})
        recent_invoices = invoices_result.fetchall()

        print(f"\n📄 Recent Invoices (last 24h): {len(recent_invoices)}")

        if recent_invoices:
            for invoice in recent_invoices:
                status_emoji = {
                    'pending': '⏳',
                    'processing': '🔄',
                    'completed': '✅',
                    'failed': '❌',
                    'needs_review': '⚠️'
                }.get(invoice.status, '❓')

                amount = f"{invoice.total_amount or 'N/A'} {invoice.currency or ''}"
                print(f"  {status_emoji} {invoice.supplier_name}")
                print(f"     Status: {invoice.status}")
                print(f"     Amount: {amount}")
                print(f"     Created: {invoice.created_at}")
                print(f"     Tenant: {invoice.tenant_id}")
                if invoice.processing_error:
                    print(f"     Error: {invoice.processing_error}")
                print()

        # Summary by status
        print(f"\n📊 Summary:")

        # Session status counts
        session_counts_query = text("""
            SELECT status, COUNT(*) as count
            FROM agent_sessions
            WHERE created_at >= :cutoff_time
            GROUP BY status
        """)

        session_counts_result = db.execute(session_counts_query, {"cutoff_time": cutoff_time})
        session_counts = dict(session_counts_result.fetchall())

        print(f"  AI Agent Sessions (24h):")
        for status, count in session_counts.items():
            print(f"    {status}: {count}")

        # Invoice status counts
        invoice_counts_query = text("""
            SELECT status, COUNT(*) as count
            FROM invoices
            WHERE created_at >= :cutoff_time
            GROUP BY status
        """)

        invoice_counts_result = db.execute(invoice_counts_query, {"cutoff_time": cutoff_time})
        invoice_counts = dict(invoice_counts_result.fetchall())

        print(f"  Invoices (24h):")
        for status, count in invoice_counts.items():
            print(f"    {status}: {count}")

        # Check tenants
        tenant_count_query = text("SELECT COUNT(*) FROM tenants WHERE is_active = true")
        tenant_count_result = db.execute(tenant_count_query)
        tenant_count = tenant_count_result.scalar()
        print(f"  Active tenants: {tenant_count}")

        if not recent_sessions and not recent_invoices:
            print("\n💡 No recent activity found.")
            print("   This could mean:")
            print("   1. Manual Sync didn't find any new invoices")
            print("   2. Manual Sync failed silently")
            print("   3. AI Agent processing is still pending")
            print("   4. There's an issue with the integration")

    except Exception as e:
        print(f"❌ Error checking database: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    check_recent_activity()
