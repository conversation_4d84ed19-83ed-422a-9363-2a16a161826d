#!/usr/bin/env python3
"""
Detaljerad analys av den aktuella sessionen
"""

import sys
import os
from datetime import datetime
sys.path.append('apps/backend-api')

def debug_current_session():
    """Analysera den aktuella sessionen i detalj"""
    print('🔍 DEBUGGING CURRENT SESSION')
    print('=' * 50)
    
    try:
        # Use Docker database connection directly
        from sqlalchemy import create_engine, text
        from sqlalchemy.orm import sessionmaker
        
        docker_db_url = "postgresql://postgres:postgres@localhost:5432/aggie_dev"
        engine = create_engine(docker_db_url)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        
        db = SessionLocal()
        
        # Find the most recent session
        session_query = text("""
            SELECT id, status, current_step_index, started_at, source_metadata, 
                   created_at, retry_count, max_retries, requires_human_review, action_item_reason
            FROM agent_sessions 
            ORDER BY created_at DESC
            LIMIT 1
        """)
        
        session_result = db.execute(session_query).fetchone()
        
        if not session_result:
            print('❌ No sessions found!')
            return
        
        session_id = str(session_result[0])
        status = session_result[1]
        current_step = session_result[2]
        started_at = session_result[3]
        source_metadata = session_result[4]
        created_at = session_result[5]
        retry_count = session_result[6]
        max_retries = session_result[7]
        requires_review = session_result[8]
        action_reason = session_result[9]
        
        print(f'📋 SESSION: {session_id}')
        print(f'  Status: {status}')
        print(f'  Current Step Index: {current_step}')
        print(f'  Created: {created_at}')
        print(f'  Started: {started_at}')
        print(f'  Retry Count: {retry_count}/{max_retries}')
        print(f'  Requires Review: {requires_review}')
        
        # Get all execution steps for this session
        steps_query = text("""
            SELECT step_name, status, step_index, started_at, completed_at, 
                   input_data, output_data, error_message, created_at
            FROM execution_steps 
            WHERE session_id = :session_id
            ORDER BY step_index
        """)
        
        steps = db.execute(steps_query, {"session_id": session_id}).fetchall()
        
        print(f'\n📊 EXECUTION STEPS ({len(steps)}):')
        for step in steps:
            step_name = step[0]
            step_status = step[1]
            step_index = step[2]
            step_started = step[3]
            step_completed = step[4]
            input_data = step[5]
            output_data = step[6]
            error_msg = step[7]
            step_created = step[8]
            
            status_icon = {
                'PENDING': '⏳',
                'RUNNING': '🏃',
                'COMPLETED': '✅',
                'FAILED': '❌'
            }.get(step_status, '❓')
            
            current_marker = ' <-- CURRENT' if step_index == current_step else ''
            
            print(f'  {status_icon} Step {step_index}: {step_name} ({step_status}){current_marker}')
            print(f'    Created: {step_created}')
            
            if step_started:
                print(f'    Started: {step_started}')
            if step_completed:
                print(f'    Completed: {step_completed}')
            if error_msg:
                print(f'    ❌ Error: {error_msg}')
            
            # Show input/output for problematic steps
            if step_status in ['FAILED', 'RUNNING'] or step_index == current_step:
                if input_data:
                    print(f'    📥 INPUT: {input_data}')
                if output_data:
                    print(f'    📤 OUTPUT: {output_data}')
        
        # Get tool results
        tool_results_query = text("""
            SELECT tool_name, success, error_message, created_at, output_data, input_parameters
            FROM tool_results 
            WHERE session_id = :session_id
            ORDER BY created_at
        """)
        
        tool_results = db.execute(tool_results_query, {"session_id": session_id}).fetchall()
        
        print(f'\n🔧 TOOL RESULTS ({len(tool_results)}):')
        for i, result in enumerate(tool_results):
            tool_name = result[0]
            success = result[1]
            error_msg = result[2]
            created = result[3]
            output = result[4]
            input_params = result[5]
            
            status_icon = '✅' if success else '❌'
            print(f'  {i+1}. {status_icon} {tool_name} at {created}')
            
            if error_msg:
                print(f'     ❌ Error: {error_msg}')
            
            if input_params:
                print(f'     📥 Input: {input_params}')
            
            if output:
                print(f'     📤 Output: {output}')
        
        # Get recent thoughts
        thoughts_query = text("""
            SELECT thought_type, content, sequence_number, created_at
            FROM thought_chains 
            WHERE session_id = :session_id
            ORDER BY sequence_number
        """)
        
        thoughts = db.execute(thoughts_query, {"session_id": session_id}).fetchall()
        
        print(f'\n🧠 THOUGHT CHAIN ({len(thoughts)}):')
        for thought in thoughts:
            thought_type = thought[0]
            content = thought[1]
            seq = thought[2]
            created = thought[3]
            
            print(f'  {seq}. {thought_type} at {created}')
            if isinstance(content, dict):
                message = content.get('message', 'No message')
                print(f'     {message}')
                if 'error' in content:
                    print(f'     ❌ Error: {content["error"]}')
                if 'current_step' in content:
                    print(f'     📍 Current Step: {content["current_step"]}')
        
        db.close()
        
        # Analysis
        print(f'\n🔍 ANALYSIS:')
        
        # Check step execution order
        completed_steps = [s for s in steps if s[1] == 'COMPLETED']
        failed_steps = [s for s in steps if s[1] == 'FAILED']
        running_steps = [s for s in steps if s[1] == 'RUNNING']
        
        print(f'  ✅ Completed steps: {[s[0] for s in completed_steps]}')
        print(f'  ❌ Failed steps: {[s[0] for s in failed_steps]}')
        print(f'  🏃 Running steps: {[s[0] for s in running_steps]}')
        
        # Check if steps are in correct order
        if completed_steps:
            last_completed_index = max(s[2] for s in completed_steps)
            print(f'  📍 Last completed step index: {last_completed_index}')
            print(f'  📍 Current step index: {current_step}')
            
            if current_step != last_completed_index + 1:
                print(f'  ⚠️  PROBLEM: Current step index ({current_step}) is not sequential after last completed ({last_completed_index})')
        
        # Check for missing extract_content output
        get_invoice_result = None
        extract_content_result = None
        
        for result in tool_results:
            if result[0] == 'get_invoice' and result[1]:  # successful
                get_invoice_result = result[4]  # output_data
            elif result[0] == 'extract_content' and result[1]:  # successful
                extract_content_result = result[4]  # output_data
        
        if get_invoice_result:
            print(f'  ✅ get_invoice output available')
        else:
            print(f'  ❌ get_invoice output missing')
        
        if extract_content_result:
            print(f'  ✅ extract_content output available')
        else:
            print(f'  ❌ extract_content output missing - this is why web_search fails!')
        
    except Exception as e:
        print(f'❌ Analysis error: {e}')

def main():
    debug_current_session()

if __name__ == "__main__":
    main()
