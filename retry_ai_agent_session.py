#!/usr/bin/env python3
"""
Script to retry a failed AI Agent session
"""

import requests
import json

# Configuration
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"

def get_auth_token():
    """Get authentication token by logging in"""
    try:
        login_data = {
            "email": "<EMAIL>",
            "password": "admin123"
        }
        
        response = requests.post(f"{API_BASE}/auth/token", json=login_data)
        if response.status_code == 200:
            result = response.json()
            return result.get("access_token")
        else:
            print(f"❌ Login failed: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Error during login: {e}")
        return None

def retry_session(session_id: str):
    """Retry a failed AI Agent session"""
    print(f"🔄 Retrying AI Agent session: {session_id}")
    
    # Get auth token
    token = get_auth_token()
    if not token:
        return
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json",
        "X-Tenant-ID": "94311644-a034-4967-87ea-bc46068ab6be"
    }
    
    try:
        # Retry the session
        response = requests.post(f"{API_BASE}/ai-agent/sessions/{session_id}/retry", headers=headers)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Session retry started successfully!")
            print(f"Result: {json.dumps(result, indent=2)}")
        else:
            print(f"❌ Session retry failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error during session retry: {e}")

def main():
    # The session ID from our previous test
    session_id = "257914ea-f467-418b-b334-b11f75f04727"
    
    print("🚀 AI Agent Session Retry Test")
    print("=" * 50)
    
    retry_session(session_id)

if __name__ == "__main__":
    main()
