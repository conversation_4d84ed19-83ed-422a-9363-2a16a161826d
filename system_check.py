#!/usr/bin/env python3
"""
Systemkontroll för AI Agent felsökning
"""

import sys
import os
sys.path.append('apps/backend-api')

def check_database():
    """Kontrollera databasanslutning och AI Agent tabeller"""
    print('🔍 TESTING DATABASE CONNECTION...')
    try:
        # Use Docker database connection directly
        from sqlalchemy import create_engine, text
        from sqlalchemy.orm import sessionmaker

        # Connect to Docker database
        docker_db_url = "postgresql://postgres:postgres@localhost:5432/aggie_dev"
        engine = create_engine(docker_db_url)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

        db = SessionLocal()
        result = db.execute(text('SELECT 1')).scalar()
        print(f'✅ Database connection: OK (result: {result})')
        
        # Check if AI agent tables exist
        tables_query = text("""
            SELECT table_name FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name IN ('agent_sessions', 'execution_steps', 'tool_results', 'thought_chains')
            ORDER BY table_name
        """)
        
        tables = db.execute(tables_query).fetchall()
        table_names = [t[0] for t in tables]
        print(f'📋 AI Agent tables found: {table_names}')
        
        if len(table_names) < 4:
            print('❌ Missing AI Agent tables! Expected: agent_sessions, execution_steps, tool_results, thought_chains')
            return False
        
        # Check recent sessions
        sessions_query = text("""
            SELECT id, status, current_step_index, created_at, source_type
            FROM agent_sessions 
            ORDER BY created_at DESC 
            LIMIT 5
        """)
        
        sessions = db.execute(sessions_query).fetchall()
        print(f'\n📊 Recent sessions ({len(sessions)}):')
        for session in sessions:
            session_id = str(session[0])[:8] if session[0] else "N/A"
            print(f'  - {session_id}... | {session[1]} | Step {session[2]} | {session[4]} | {session[3]}')
        
        # Check running sessions
        running_query = text("""
            SELECT id, status, current_step_index, started_at, source_metadata
            FROM agent_sessions
            WHERE status = 'RUNNING'
            ORDER BY started_at DESC
        """)
        
        running_sessions = db.execute(running_query).fetchall()
        print(f'\n🏃 Running sessions ({len(running_sessions)}):')
        for session in running_sessions:
            session_id = str(session[0])[:8] if session[0] else "N/A"
            print(f'  - {session_id}... | Step {session[2]} | Started: {session[3]}')
            if session[4]:
                metadata = session[4]
                if isinstance(metadata, dict):
                    print(f'    File: {metadata.get("file_path", "N/A")}')
        
        db.close()
        return True
        
    except Exception as e:
        print(f'❌ Database error: {e}')
        return False

def check_redis():
    """Kontrollera Redis-anslutning"""
    print('\n🔍 TESTING REDIS CONNECTION...')
    try:
        import redis

        # Connect to Docker Redis directly
        r = redis.from_url("redis://localhost:6379/0")
        r.ping()
        print('✅ Redis connection: OK')
        
        # Check Celery queues
        try:
            queue_info = r.llen('celery')
            print(f'📋 Celery queue length: {queue_info}')
        except:
            print('📋 Celery queue info not available')
        
        return True
        
    except Exception as e:
        print(f'❌ Redis error: {e}')
        return False

def check_upload_directory():
    """Kontrollera upload-mappen"""
    print('\n📁 CHECKING UPLOAD DIRECTORY...')
    try:
        from app.config import settings
        upload_dir = settings.upload_dir
        print(f'Upload directory: {upload_dir}')
        
        if os.path.exists(upload_dir):
            print('✅ Upload directory exists')
            
            # List tenant directories
            try:
                subdirs = [d for d in os.listdir(upload_dir) if os.path.isdir(os.path.join(upload_dir, d))]
                print(f'📂 Tenant directories: {subdirs}')
                
                # Check files in each tenant directory
                for tenant_dir in subdirs:
                    tenant_path = os.path.join(upload_dir, tenant_dir)
                    files = [f for f in os.listdir(tenant_path) if os.path.isfile(os.path.join(tenant_path, f))]
                    print(f'  📄 {tenant_dir}: {len(files)} files')
                    
                    # Show recent files
                    if files:
                        recent_files = sorted(files, key=lambda f: os.path.getmtime(os.path.join(tenant_path, f)), reverse=True)[:3]
                        for file in recent_files:
                            file_path = os.path.join(tenant_path, file)
                            size = os.path.getsize(file_path)
                            print(f'    - {file} ({size} bytes)')
                
                return True
                
            except Exception as e:
                print(f'❌ Error listing upload directory: {e}')
                return False
        else:
            print('❌ Upload directory does not exist')
            return False
            
    except Exception as e:
        print(f'❌ Upload directory check error: {e}')
        return False

def check_celery_workers():
    """Kontrollera Celery workers via Docker"""
    print('\n🔍 CHECKING CELERY WORKERS...')
    try:
        import subprocess

        # Check via Docker instead of importing Celery (avoids recursion)
        result = subprocess.run(
            ['docker', 'exec', 'aggie-worker-1', 'celery', '-A', 'app.celery_app', 'inspect', 'active'],
            capture_output=True, text=True, timeout=10
        )

        if result.returncode == 0:
            if 'empty' in result.stdout:
                print('✅ Celery worker active (no running tasks)')
            else:
                print('✅ Celery worker active with tasks')
                # Count tasks
                lines = result.stdout.split('\n')
                task_lines = [l for l in lines if 'name' in l]
                print(f'  📋 {len(task_lines)} active tasks')
            return True
        else:
            print(f'❌ Celery worker check failed: {result.stderr}')
            return False

    except subprocess.TimeoutExpired:
        print('❌ Celery check timeout')
        return False
    except Exception as e:
        print(f'❌ Celery check error: {e}')
        return False

def check_ai_agent_config():
    """Kontrollera AI Agent konfiguration"""
    print('\n🔍 CHECKING AI AGENT CONFIGURATION...')
    try:
        # Use Docker database connection directly
        from sqlalchemy import create_engine, text
        from sqlalchemy.orm import sessionmaker

        docker_db_url = "postgresql://postgres:postgres@localhost:5432/aggie_dev"
        engine = create_engine(docker_db_url)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

        db = SessionLocal()
        
        # Check execution plans
        plans_query = text("""
            SELECT name, version, is_active, steps
            FROM execution_plans
            WHERE is_active = true
            ORDER BY created_at DESC
        """)
        
        plans = db.execute(plans_query).fetchall()
        print(f'📋 Active execution plans ({len(plans)}):')
        for plan in plans:
            steps = plan[3] if plan[3] else []
            step_names = [step.get('step', 'Unknown') for step in steps] if isinstance(steps, list) else []
            print(f'  - {plan[0]} v{plan[1]}: {step_names}')
        
        db.close()
        return True
        
    except Exception as e:
        print(f'❌ AI Agent config check error: {e}')
        return False

def main():
    """Huvudfunktion för systemkontroll"""
    print('🚀 AI AGENT SYSTEM CHECK')
    print('=' * 50)
    
    checks = [
        ('Database', check_database),
        ('Redis', check_redis),
        ('Upload Directory', check_upload_directory),
        ('Celery Workers', check_celery_workers),
        ('AI Agent Config', check_ai_agent_config)
    ]
    
    results = {}
    
    for check_name, check_func in checks:
        try:
            results[check_name] = check_func()
        except Exception as e:
            print(f'❌ {check_name} check failed: {e}')
            results[check_name] = False
    
    print('\n' + '=' * 50)
    print('📊 SYSTEM CHECK SUMMARY')
    print('=' * 50)
    
    for check_name, result in results.items():
        status = '✅ PASS' if result else '❌ FAIL'
        print(f'{check_name:<20}: {status}')
    
    failed_checks = [name for name, result in results.items() if not result]
    
    if failed_checks:
        print(f'\n⚠️  FAILED CHECKS: {", ".join(failed_checks)}')
        print('These issues may be causing the AI Agent to get stuck on get_invoice step.')
    else:
        print('\n🎉 All system checks passed!')

if __name__ == "__main__":
    main()
