import React, { useState } from 'react';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import {
  ArrowPathIcon,
  CheckCircleIcon,
  XCircleIcon,
  CloudArrowDownIcon
} from '@heroicons/react/24/outline';
import { integrationsApi } from '../services/api';
import { IntegrationSyncResult } from '../types';
import toast from 'react-hot-toast';

interface ManualSyncProps {
  className?: string;
}

export default function ManualSync({ className = '' }: ManualSyncProps) {
  const [syncResults, setSyncResults] = useState<IntegrationSyncResult[]>([]);
  const [showResults, setShowResults] = useState(false);
  const queryClient = useQueryClient();

  const { data: integrations } = useQuery(
    'integrations',
    integrationsApi.getIntegrations
  );

  const syncAllMutation = useMutation(integrationsApi.syncAllIntegrations, {
    onSuccess: (results) => {
      setSyncResults(results);
      setShowResults(true);
      queryClient.invalidateQueries('integrations');
      
      const successCount = results.filter(r => r.success).length;
      const totalCount = results.length;
      
      if (successCount === totalCount) {
        toast.success(`Successfully synced all ${totalCount} integrations`);
      } else {
        toast.error(`Synced ${successCount}/${totalCount} integrations`);
      }
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.detail || 'Failed to sync integrations');
    },
  });

  const handleSyncAll = () => {
    setShowResults(false);
    syncAllMutation.mutate();
  };



  const getResultIcon = (result: IntegrationSyncResult) => {
    if (result.success) {
      return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
    } else {
      return <XCircleIcon className="h-5 w-5 text-red-500" />;
    }
  };

  const activeIntegrations = integrations?.filter(i => i.is_active) || [];
  const isLoading = syncAllMutation.isLoading;

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="card-container">
        <h3 className="section-title flex items-center mb-6">
          <ArrowPathIcon className="h-6 w-6 mr-2 text-indigo-600" />
          Manual Sync
        </h3>

        <div className="bg-gradient-to-r from-green-50 via-blue-50 to-indigo-50 rounded-2xl p-6">
          {activeIntegrations.length === 0 ? (
            <div className="text-center py-8">
              <CloudArrowDownIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 mb-4">No active integration found</p>
              <p className="text-sm text-gray-400">
                Please configure and activate an integration to enable manual sync.
              </p>
            </div>
          ) : (
            <div>
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Sync Integration</h4>
                  <p className="text-sm text-gray-600">
                    Manually trigger sync for your active integration
                  </p>
                </div>
                <button
                  onClick={handleSyncAll}
                  disabled={isLoading}
                  className="btn-primary flex items-center !w-40"
                >
                  {syncAllMutation.isLoading ? (
                    <ArrowPathIcon className="h-5 w-5 mr-2 animate-spin" />
                  ) : (
                    <CloudArrowDownIcon className="h-5 w-5 mr-2" />
                  )}
                  {syncAllMutation.isLoading ? 'Syncing...' : 'Sync Now'}
                </button>
              </div>
            </div>
          )}




        </div>
      </div>

      {/* Sync Results */}
      {showResults && syncResults.length > 0 && (
        <div className="card-container">
          <h4 className="section-title mb-4">Sync Results</h4>
          <div className="space-y-3">
            {syncResults.map((result, index) => (
              <div
                key={`${result.integration_id}-${index}`}
                className={`p-4 rounded-lg border ${
                  result.success 
                    ? 'bg-green-50 border-green-200' 
                    : 'bg-red-50 border-red-200'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {getResultIcon(result)}
                    <div>
                      <span className="font-medium text-gray-900">
                        {result.integration_type}
                      </span>
                      <p className="text-sm text-gray-600">{result.message}</p>
                    </div>
                  </div>
                  {result.success && (
                    <div className="text-right">
                      <span className="text-sm font-medium text-gray-900">
                        {result.invoices_count} invoices
                      </span>
                      {result.synced_at && (
                        <p className="text-xs text-gray-500">
                          {new Date(result.synced_at).toLocaleString()}
                        </p>
                      )}
                    </div>
                  )}
                </div>
                
                {result.error_details && (
                  <div className="mt-3 p-3 bg-red-100 rounded-lg">
                    <pre className="text-xs text-red-700 whitespace-pre-wrap">
                      {JSON.stringify(result.error_details, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            ))}
          </div>
          
          <div className="mt-4 flex justify-end">
            <button
              onClick={() => setShowResults(false)}
              className="btn-secondary"
            >
              Close Results
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
