#!/usr/bin/env python3
"""
Rensa fastnade sessioner och testa AI Agent
"""

import sys
import os
from datetime import datetime
sys.path.append('apps/backend-api')

def clean_stuck_sessions():
    """Rensa fastnade sessioner"""
    print('🧹 CLEANING STUCK AI AGENT SESSIONS')
    print('=' * 50)
    
    try:
        # Use Docker database connection directly
        from sqlalchemy import create_engine, text
        from sqlalchemy.orm import sessionmaker
        
        docker_db_url = "postgresql://postgres:postgres@localhost:5432/aggie_dev"
        engine = create_engine(docker_db_url)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        
        db = SessionLocal()
        
        # Find stuck sessions (running > 5 minutes)
        stuck_query = text("""
            UPDATE agent_sessions 
            SET status = 'FAILED',
                requires_human_review = true,
                action_item_reason = 'Session stuck - cleaned up automatically',
                completed_at = NOW()
            WHERE status = 'RUNNING' 
            AND started_at < NOW() - INTERVAL '5 minutes'
            RETURNING id, started_at
        """)
        
        result = db.execute(stuck_query)
        stuck_sessions = result.fetchall()
        db.commit()
        
        if stuck_sessions:
            print(f'✅ Cleaned {len(stuck_sessions)} stuck sessions:')
            for session in stuck_sessions:
                session_id = str(session[0])[:8]
                started = session[1]
                print(f'  - {session_id}... (started: {started})')
        else:
            print('✅ No stuck sessions found to clean')
        
        # Also clean any running execution steps
        stuck_steps_query = text("""
            UPDATE execution_steps 
            SET status = 'FAILED',
                completed_at = NOW()
            WHERE status = 'RUNNING' 
            AND started_at < NOW() - INTERVAL '5 minutes'
            RETURNING id, step_name, started_at
        """)
        
        result = db.execute(stuck_steps_query)
        stuck_steps = result.fetchall()
        db.commit()
        
        if stuck_steps:
            print(f'✅ Cleaned {len(stuck_steps)} stuck execution steps:')
            for step in stuck_steps:
                step_id = str(step[0])[:8]
                step_name = step[1]
                started = step[2]
                print(f'  - {step_id}... {step_name} (started: {started})')
        
        db.close()
        return True
        
    except Exception as e:
        print(f'❌ Error cleaning sessions: {e}')
        return False

def restart_backend_container():
    """Starta om backend-containern för att ladda den fixade koden"""
    print('\n🔄 RESTARTING BACKEND CONTAINER...')
    
    try:
        import subprocess
        
        # Restart backend container
        result = subprocess.run(['docker', 'restart', 'aggie-backend-api-1'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print('✅ Backend container restarted successfully')
            
            # Wait a moment for it to start
            import time
            print('⏳ Waiting for container to start...')
            time.sleep(10)
            
            # Check if it's running
            check_result = subprocess.run(['docker', 'ps', '--filter', 'name=aggie-backend-api-1'], 
                                        capture_output=True, text=True)
            
            if 'aggie-backend-api-1' in check_result.stdout:
                print('✅ Backend container is running')
                return True
            else:
                print('❌ Backend container failed to start')
                return False
        else:
            print(f'❌ Failed to restart container: {result.stderr}')
            return False
            
    except Exception as e:
        print(f'❌ Error restarting container: {e}')
        return False

def restart_worker_container():
    """Starta om worker-containern"""
    print('\n🔄 RESTARTING WORKER CONTAINER...')
    
    try:
        import subprocess
        
        # Restart worker container
        result = subprocess.run(['docker', 'restart', 'aggie-worker-1'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print('✅ Worker container restarted successfully')
            
            # Wait a moment for it to start
            import time
            print('⏳ Waiting for worker to start...')
            time.sleep(10)
            
            return True
        else:
            print(f'❌ Failed to restart worker: {result.stderr}')
            return False
            
    except Exception as e:
        print(f'❌ Error restarting worker: {e}')
        return False

def test_ai_agent():
    """Testa AI Agent genom att skapa en ny session"""
    print('\n🧪 TESTING AI AGENT...')
    
    try:
        # Use Docker database connection directly
        from sqlalchemy import create_engine, text
        from sqlalchemy.orm import sessionmaker
        
        docker_db_url = "postgresql://postgres:postgres@localhost:5432/aggie_dev"
        engine = create_engine(docker_db_url)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        
        db = SessionLocal()
        
        # Check if there are any recent successful sessions
        success_query = text("""
            SELECT id, status, completed_at, created_at
            FROM agent_sessions 
            WHERE status = 'COMPLETED'
            ORDER BY completed_at DESC
            LIMIT 3
        """)
        
        successful_sessions = db.execute(success_query).fetchall()
        
        print(f'📊 Recent successful sessions ({len(successful_sessions)}):')
        for session in successful_sessions:
            session_id = str(session[0])[:8]
            status = session[1]
            completed = session[2]
            created = session[3]
            print(f'  ✅ {session_id}... | {status} | Completed: {completed}')
        
        # Check current running sessions
        running_query = text("""
            SELECT id, status, current_step_index, started_at
            FROM agent_sessions 
            WHERE status = 'RUNNING'
            ORDER BY started_at DESC
        """)
        
        running_sessions = db.execute(running_query).fetchall()
        
        print(f'\n🏃 Currently running sessions ({len(running_sessions)}):')
        for session in running_sessions:
            session_id = str(session[0])[:8]
            status = session[1]
            step = session[2]
            started = session[3]
            print(f'  🏃 {session_id}... | Step {step} | Started: {started}')
        
        db.close()
        
        if running_sessions:
            print('\n⚠️  There are still running sessions. Monitor them to see if they complete.')
        else:
            print('\n✅ No running sessions. AI Agent is ready for new tasks.')
        
        return True
        
    except Exception as e:
        print(f'❌ Error testing AI Agent: {e}')
        return False

def main():
    """Huvudfunktion för reparation"""
    print('🔧 AI AGENT REPAIR PROCESS')
    print('=' * 50)
    
    success = True
    
    # Step 1: Clean stuck sessions
    if not clean_stuck_sessions():
        success = False
    
    # Step 2: Restart backend container
    if not restart_backend_container():
        success = False
    
    # Step 3: Restart worker container
    if not restart_worker_container():
        success = False
    
    # Step 4: Test AI Agent
    if not test_ai_agent():
        success = False
    
    print('\n' + '=' * 50)
    if success:
        print('🎉 AI AGENT REPAIR COMPLETED SUCCESSFULLY!')
        print('The get_invoice step should now work correctly.')
    else:
        print('❌ REPAIR PROCESS HAD ISSUES')
        print('Please check the errors above and try manual intervention.')
    
    print('\n💡 NEXT STEPS:')
    print('1. Upload a new invoice to test the fix')
    print('2. Monitor the session progress')
    print('3. Check that get_invoice step completes successfully')

if __name__ == "__main__":
    main()
