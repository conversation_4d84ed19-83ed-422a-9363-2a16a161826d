#!/usr/bin/env python3
"""
Simple script to show AI Agent thoughts directly from database
"""

import os
import sys
from datetime import datetime

def show_ai_thoughts():
    """Show AI agent thoughts directly from database"""
    try:
        from sqlalchemy import create_engine, text
        import json
        
        # Database connection - URL encode the password
        import urllib.parse
        password = urllib.parse.quote_plus('vV@YPA1C&Ki8U3')
        db_url = f'postgresql://aggie_user:{password}@localhost:5432/aggie_prod'
        engine = create_engine(db_url)
        
        with engine.connect() as conn:
            # Get running sessions
            session_query = text('''
                SELECT id, session_name, status, current_step_index, created_at, started_at
                FROM agent_sessions 
                WHERE status = 'running'
                ORDER BY created_at DESC 
                LIMIT 5
            ''')
            
            sessions = conn.execute(session_query).fetchall()
            
            if not sessions:
                print("No running AI Agent sessions found")
                
                # Check recent sessions
                recent_query = text('''
                    SELECT id, session_name, status, current_step_index, created_at
                    FROM agent_sessions 
                    ORDER BY created_at DESC 
                    LIMIT 3
                ''')
                
                recent_sessions = conn.execute(recent_query).fetchall()
                print(f"\nRecent sessions ({len(recent_sessions)}):")
                for session in recent_sessions:
                    print(f"  - {session[1]} ({session[2]}) - {session[4]}")
                
                return
            
            for session in sessions:
                session_id, session_name, status, current_step, created_at, started_at = session
                
                print(f"=== AI Agent Session: {session_name} ===")
                print(f"Status: {status}")
                print(f"Current Step: {current_step}")
                print(f"Created: {created_at}")
                print(f"Started: {started_at}")
                print()
                
                # Get execution steps
                steps_query = text('''
                    SELECT step_name, step_index, status, started_at, completed_at
                    FROM execution_steps 
                    WHERE session_id = :session_id
                    ORDER BY step_index
                ''')
                
                steps = conn.execute(steps_query, {"session_id": session_id}).fetchall()
                
                print("Execution Steps:")
                for step in steps:
                    step_name, step_index, step_status, step_started, step_completed = step
                    
                    status_symbol = {
                        'pending': '[ ]',
                        'running': '[~]',
                        'completed': '[x]',
                        'failed': '[!]',
                        'skipped': '[-]'
                    }.get(step_status, '[?]')
                    
                    duration = ""
                    if step_started and step_completed:
                        duration = f" ({(step_completed - step_started).total_seconds():.1f}s)"
                    elif step_started:
                        duration = f" (running {(datetime.utcnow() - step_started).total_seconds():.1f}s)"
                    
                    print(f"  {status_symbol} [{step_index}] {step_name}{duration}")
                
                print()
                
                # Get recent thoughts
                thoughts_query = text('''
                    SELECT sequence_number, thought_type, content, created_at
                    FROM thought_chains 
                    WHERE session_id = :session_id
                    ORDER BY sequence_number DESC
                    LIMIT 15
                ''')
                
                thoughts = conn.execute(thoughts_query, {"session_id": session_id}).fetchall()
                
                print("Recent Thoughts (latest first):")
                print("-" * 50)
                
                for thought in thoughts:
                    seq, thought_type, content, created = thought
                    time_str = created.strftime('%H:%M:%S')
                    
                    print(f"[{seq:2d}] {thought_type.upper()} - {time_str}")
                    
                    # Parse content
                    try:
                        if isinstance(content, str):
                            content_obj = json.loads(content)
                        else:
                            content_obj = content
                        
                        if isinstance(content_obj, dict):
                            # Show key information
                            if 'message' in content_obj:
                                print(f"     Message: {content_obj['message']}")
                            
                            if 'step' in content_obj:
                                print(f"     Step: {content_obj['step']}")
                            
                            if 'tool_name' in content_obj:
                                print(f"     Tool: {content_obj['tool_name']}")
                            
                            if 'error' in content_obj:
                                print(f"     ERROR: {content_obj['error']}")
                            
                            if 'result' in content_obj:
                                result = content_obj['result']
                                if isinstance(result, dict):
                                    if 'success' in result:
                                        status_text = "SUCCESS" if result['success'] else "FAILED"
                                        print(f"     Result: {status_text}")
                                        if 'message' in result:
                                            print(f"     Details: {result['message']}")
                                    else:
                                        print(f"     Result: {str(result)[:100]}")
                                else:
                                    print(f"     Result: {str(result)[:100]}")
                            
                            # Show any other interesting fields
                            for key, value in content_obj.items():
                                if key not in ['message', 'step', 'tool_name', 'error', 'result']:
                                    if isinstance(value, (str, int, float, bool)):
                                        print(f"     {key}: {value}")
                        else:
                            print(f"     Content: {str(content_obj)[:100]}")
                    
                    except Exception as e:
                        print(f"     Raw: {str(content)[:100]}")
                    
                    print()
                
                print("=" * 60)
                print()
    
    except ImportError:
        print("Error: SQLAlchemy not available. Make sure you're in the backend environment.")
        print("Try: cd apps/backend-api && python ../../show_ai_thoughts.py")
    
    except Exception as e:
        print(f"Error: {e}")
        print("Make sure the database is running and accessible.")

if __name__ == "__main__":
    show_ai_thoughts()
