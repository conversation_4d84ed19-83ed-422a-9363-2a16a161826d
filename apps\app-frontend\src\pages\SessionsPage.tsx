import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { Link } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import {
  EyeIcon,
  ArrowPathIcon,
  TrashIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';
import { invoiceProcessingApi } from '../services/api';
import { useTenant } from '../contexts/TenantContext';
import { usePermissions } from '../hooks/usePermissions';
import { SessionStatus, SessionSummaryItem } from '../types';
import '../appCustomStyles.css';

export default function SessionsPage() {
  const { currentTenant } = useTenant();
  const { hasPermission } = usePermissions();
  const queryClient = useQueryClient();
  const [statusFilter, setStatusFilter] = useState<SessionStatus | ''>('');

  const { data: sessionsData, isLoading, error } = useQuery(
    ['sessions', currentTenant?.id, statusFilter],
    () => invoiceProcessingApi.getSessions({ 
      limit: 50, 
      offset: 0,
      status_filter: statusFilter || undefined 
    }),
    { enabled: !!currentTenant }
  );

  const retryMutation = useMutation(
    ({ sessionId, fromStep }: { sessionId: string; fromStep?: string }) => 
      invoiceProcessingApi.retrySession(sessionId, fromStep as any),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['sessions']);
        toast.success('Session retry started successfully');
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.detail || 'Failed to retry session');
      },
    }
  );

  const deleteMutation = useMutation(
    (sessionId: string) => invoiceProcessingApi.deleteSession(sessionId),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['sessions']);
        toast.success('Session deleted successfully');
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.detail || 'Failed to delete session');
      },
    }
  );

  const handleRetrySession = (session: SessionSummaryItem) => {
    if (window.confirm(`Retry processing for session ${session.id}?`)) {
      retryMutation.mutate({ sessionId: session.id });
    }
  };

  const handleDeleteSession = (session: SessionSummaryItem) => {
    if (window.confirm(`Delete session ${session.id} and its related invoice? This action cannot be undone.`)) {
      deleteMutation.mutate(session.id);
    }
  };

  const getStatusIcon = (status: SessionStatus) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'processing':
        return <ClockIcon className="h-5 w-5 text-blue-500 animate-spin" />;
      case 'pending':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      case 'failed':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: SessionStatus) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-red-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">Error loading sessions</h3>
        <p className="mt-1 text-sm text-gray-500">
          {error instanceof Error ? error.message : 'An unexpected error occurred'}
        </p>
      </div>
    );
  }

  const sessions = sessionsData?.sessions || [];
  const statistics = sessionsData?.statistics;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Processing Sessions</h1>
          <p className="mt-1 text-sm text-gray-500">
            Monitor and manage invoice processing sessions
          </p>
        </div>
      </div>

      {/* Statistics Cards */}
      {statistics && (
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          <div className="card-container">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CheckCircleIcon className="h-8 w-8 text-green-500" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Completed</dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {statistics.sessions_by_status.completed || 0}
                  </dd>
                </dl>
              </div>
            </div>
          </div>

          <div className="card-container">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ClockIcon className="h-8 w-8 text-blue-500" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Processing</dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {statistics.sessions_by_status.processing || 0}
                  </dd>
                </dl>
              </div>
            </div>
          </div>

          <div className="card-container">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <XCircleIcon className="h-8 w-8 text-red-500" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Failed</dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {statistics.sessions_by_status.failed || 0}
                  </dd>
                </dl>
              </div>
            </div>
          </div>

          <div className="card-container">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="h-8 w-8 bg-indigo-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-medium">
                    {Math.round(statistics.success_rate)}%
                  </span>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Success Rate</dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {statistics.success_rate.toFixed(1)}%
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="flex space-x-4">
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value as SessionStatus | '')}
          className="block w-48 pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
        >
          <option value="">All Statuses</option>
          <option value="pending">Pending</option>
          <option value="processing">Processing</option>
          <option value="completed">Completed</option>
          <option value="failed">Failed</option>
        </select>
      </div>

      {/* Sessions Table */}
      <div className="card-container">
        <div className="overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Session
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Invoice
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Current Step
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Logs
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Created
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {sessions.map((session) => (
                <tr key={session.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {session.id.substring(0, 8)}...
                    </div>
                    <div className="text-sm text-gray-500">
                      Invoice: {session.invoice_id.substring(0, 8)}...
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {session.invoice?.supplier_name || 'Unknown'}
                    </div>
                    <div className="text-sm text-gray-500">
                      {session.invoice?.original_filename || 'No filename'}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {getStatusIcon(session.status)}
                      <span className={`ml-2 inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(session.status)}`}>
                        {session.status}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {session.current_step || '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {session.log_count} logs
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(session.created_at).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                    <Link
                      to={`/sessions/${session.id}`}
                      className="text-indigo-600 hover:text-indigo-900"
                    >
                      <EyeIcon className="h-4 w-4 inline" />
                    </Link>
                    {session.status === 'failed' && hasPermission('invoices:write') && (
                      <button
                        onClick={() => handleRetrySession(session)}
                        disabled={retryMutation.isLoading}
                        className="text-blue-600 hover:text-blue-900 disabled:opacity-50"
                      >
                        <ArrowPathIcon className="h-4 w-4 inline" />
                      </button>
                    )}
                    {hasPermission('invoices:write') && (
                      <button
                        onClick={() => handleDeleteSession(session)}
                        disabled={deleteMutation.isLoading}
                        className="text-red-600 hover:text-red-900 disabled:opacity-50"
                      >
                        <TrashIcon className="h-4 w-4 inline" />
                      </button>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {sessions.length === 0 && (
            <div className="text-center py-12">
              <ClockIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No sessions found</h3>
              <p className="mt-1 text-sm text-gray-500">
                Upload an invoice to start processing.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
