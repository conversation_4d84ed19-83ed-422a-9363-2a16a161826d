#!/usr/bin/env python3
"""
Test LLM Provider initialization
"""

import sys
import os
sys.path.append('apps/backend-api')

def test_llm_provider():
    """Test LLM Provider initialization"""
    try:
        from app.services.llm_provider import get_provider_instance
        
        print("🔍 Testing LLM Provider initialization...")
        
        # Try to get provider instance
        provider = get_provider_instance()
        print(f"✅ LLM Provider initialized successfully: {type(provider).__name__}")
        
        # Test a simple extraction
        test_text = "Invoice #12345 from Test Company dated 2024-01-01 for $100.00"
        print(f"🧪 Testing extract_context with: {test_text}")
        
        # This will test if OpenAI client works
        import asyncio
        result = asyncio.run(provider.extract_context(test_text))
        print(f"✅ Extract context successful: {result[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ LLM Provider test failed: {e}")
        return False

if __name__ == "__main__":
    success = test_llm_provider()
    sys.exit(0 if success else 1)
