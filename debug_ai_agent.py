#!/usr/bin/env python3
"""
Script för att felsöka AI Agent och undersöka varför get_invoice steget fastnar
"""

import requests
import json
import time
import sys
from datetime import datetime
from typing import Optional, List, Dict, Any

# Konfiguration
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"

def get_auth_headers() -> Dict[str, str]:
    """Hämta auth headers - du behöver anpassa detta för din autentisering"""
    # TODO: Lägg till din autentiseringslogik här
    return {
        "Authorization": "Bearer YOUR_TOKEN_HERE",
        "Content-Type": "application/json"
    }

def get_running_sessions() -> List[Dict[str, Any]]:
    """Hämta alla körande sessioner"""
    try:
        response = requests.get(
            f"{API_BASE}/ai-agent/sessions",
            headers=get_auth_headers(),
            params={"status": "running"}
        )
        response.raise_for_status()
        return response.json()
    except Exception as e:
        print(f"❌ Fel vid hämtning av sessioner: {e}")
        return []

def get_pending_sessions() -> List[Dict[str, Any]]:
    """Hämta alla väntande sessioner"""
    try:
        response = requests.get(
            f"{API_BASE}/ai-agent/sessions",
            headers=get_auth_headers(),
            params={"status": "pending"}
        )
        response.raise_for_status()
        return response.json()
    except Exception as e:
        print(f"❌ Fel vid hämtning av väntande sessioner: {e}")
        return []

def get_session_detail(session_id: str) -> Optional[Dict[str, Any]]:
    """Hämta detaljerad information om en session"""
    try:
        response = requests.get(
            f"{API_BASE}/ai-agent/sessions/{session_id}/detail",
            headers=get_auth_headers()
        )
        response.raise_for_status()
        return response.json()
    except Exception as e:
        print(f"❌ Fel vid hämtning av sessiondetaljer: {e}")
        return None

def get_session_thoughts(session_id: str) -> List[Dict[str, Any]]:
    """Hämta thought chain för en session"""
    try:
        response = requests.get(
            f"{API_BASE}/ai-agent/sessions/{session_id}/thoughts",
            headers=get_auth_headers()
        )
        response.raise_for_status()
        return response.json()
    except Exception as e:
        print(f"❌ Fel vid hämtning av thoughts: {e}")
        return []

def get_session_tool_results(session_id: str) -> List[Dict[str, Any]]:
    """Hämta tool results för en session"""
    try:
        response = requests.get(
            f"{API_BASE}/ai-agent/sessions/{session_id}/tool-results",
            headers=get_auth_headers()
        )
        response.raise_for_status()
        return response.json()
    except Exception as e:
        print(f"❌ Fel vid hämtning av tool results: {e}")
        return []

def analyze_get_invoice_step(session_detail: Dict[str, Any]) -> None:
    """Analysera get_invoice steget specifikt"""
    print("\n🔍 ANALYS AV GET_INVOICE STEGET")
    print("=" * 50)
    
    steps = session_detail.get("steps", [])
    get_invoice_step = None
    
    # Hitta get_invoice steget
    for step in steps:
        if step.get("step_name") == "get_invoice":
            get_invoice_step = step
            break
    
    if not get_invoice_step:
        print("❌ get_invoice steget hittades inte!")
        return
    
    print(f"📋 Step Status: {get_invoice_step.get('status')}")
    print(f"📅 Started At: {get_invoice_step.get('started_at')}")
    print(f"📅 Completed At: {get_invoice_step.get('completed_at')}")
    print(f"🔢 Step Index: {get_invoice_step.get('step_index')}")
    
    # Visa input data
    input_data = get_invoice_step.get("input_data", {})
    print(f"\n📥 INPUT DATA:")
    print(json.dumps(input_data, indent=2, ensure_ascii=False))
    
    # Visa output data
    output_data = get_invoice_step.get("output_data", {})
    print(f"\n📤 OUTPUT DATA:")
    print(json.dumps(output_data, indent=2, ensure_ascii=False))
    
    # Analysera source metadata
    session_context = input_data.get("session_context", {})
    source_metadata = session_context.get("source_metadata", {})
    
    print(f"\n🗂️ SOURCE METADATA ANALYS:")
    print(f"  Source Type: {session_context.get('source_type')}")
    print(f"  Invoice DB ID: {source_metadata.get('invoice_db_id')}")
    print(f"  File Path: {source_metadata.get('file_path')}")
    print(f"  File Type: {source_metadata.get('file_type')}")
    print(f"  Filename: {source_metadata.get('filename')}")
    
    # Kontrollera om filen existerar
    file_path = source_metadata.get('file_path')
    if file_path:
        import os
        if os.path.exists(file_path):
            print(f"  ✅ Filen existerar: {file_path}")
            file_size = os.path.getsize(file_path)
            print(f"  📏 Filstorlek: {file_size} bytes")
        else:
            print(f"  ❌ Filen existerar INTE: {file_path}")

def analyze_session_thoughts(thoughts: List[Dict[str, Any]]) -> None:
    """Analysera thought chain för att hitta problem"""
    print("\n🧠 THOUGHT CHAIN ANALYS")
    print("=" * 50)
    
    if not thoughts:
        print("❌ Inga thoughts hittades!")
        return
    
    # Gruppera thoughts per typ
    thought_types = {}
    errors = []
    get_invoice_thoughts = []
    
    for thought in thoughts:
        thought_type = thought.get("thought_type")
        content = thought.get("content", {})
        
        # Räkna typer
        thought_types[thought_type] = thought_types.get(thought_type, 0) + 1
        
        # Samla fel
        if "error" in str(content).lower():
            errors.append(thought)
        
        # Samla get_invoice relaterade thoughts
        if "get_invoice" in str(content).lower():
            get_invoice_thoughts.append(thought)
    
    print(f"📊 Thought Types: {thought_types}")
    
    if errors:
        print(f"\n❌ ERRORS FOUND ({len(errors)}):")
        for error in errors[-3:]:  # Visa senaste 3 fel
            print(f"  Seq {error.get('sequence_number')}: {error.get('content')}")
    
    if get_invoice_thoughts:
        print(f"\n🎯 GET_INVOICE THOUGHTS ({len(get_invoice_thoughts)}):")
        for thought in get_invoice_thoughts[-5:]:  # Visa senaste 5
            print(f"  Seq {thought.get('sequence_number')} ({thought.get('thought_type')}): {thought.get('content')}")

def analyze_tool_results(tool_results: List[Dict[str, Any]]) -> None:
    """Analysera tool results"""
    print("\n🔧 TOOL RESULTS ANALYS")
    print("=" * 50)
    
    if not tool_results:
        print("❌ Inga tool results hittades!")
        return
    
    get_invoice_results = [r for r in tool_results if r.get("tool_name") == "get_invoice"]
    
    if not get_invoice_results:
        print("❌ Inga get_invoice tool results hittades!")
        return
    
    for result in get_invoice_results:
        print(f"🔧 Tool: {result.get('tool_name')}")
        print(f"✅ Success: {result.get('success')}")
        print(f"⏱️ Execution Time: {result.get('execution_time_ms')}ms")
        print(f"📥 Input: {json.dumps(result.get('input_data', {}), indent=2, ensure_ascii=False)}")
        print(f"📤 Output: {json.dumps(result.get('output_data', {}), indent=2, ensure_ascii=False)}")
        if result.get('error_message'):
            print(f"❌ Error: {result.get('error_message')}")

def main():
    """Huvudfunktion för felsökning"""
    print("🚀 AI AGENT FELSÖKNING - GET_INVOICE STEGET")
    print("=" * 60)
    
    # Hämta körande sessioner
    print("\n📋 Hämtar körande sessioner...")
    running_sessions = get_running_sessions()
    
    if not running_sessions:
        print("❌ Inga körande sessioner hittades!")
        
        # Kolla väntande sessioner
        print("\n📋 Hämtar väntande sessioner...")
        pending_sessions = get_pending_sessions()
        
        if pending_sessions:
            print(f"⏳ Hittade {len(pending_sessions)} väntande sessioner:")
            for session in pending_sessions:
                print(f"  - {session.get('id')}: {session.get('session_name')}")
        else:
            print("❌ Inga väntande sessioner hittades!")
        
        return
    
    print(f"✅ Hittade {len(running_sessions)} körande sessioner:")
    for session in running_sessions:
        print(f"  - {session.get('id')}: {session.get('session_name')} (Step {session.get('current_step_index')})")
    
    # Analysera första körande sessionen
    session_id = running_sessions[0].get('id')
    print(f"\n🔍 Analyserar session: {session_id}")
    
    # Hämta detaljerad information
    session_detail = get_session_detail(session_id)
    if not session_detail:
        print("❌ Kunde inte hämta sessiondetaljer!")
        return
    
    print(f"\n📊 SESSION ÖVERSIKT")
    print(f"  Status: {session_detail.get('status')}")
    print(f"  Current Step: {session_detail.get('current_step_index')}")
    print(f"  Total Steps: {len(session_detail.get('steps', []))}")
    print(f"  Retry Count: {session_detail.get('retry_count')}")
    print(f"  Source Type: {session_detail.get('source_type')}")
    
    # Analysera get_invoice steget
    analyze_get_invoice_step(session_detail)
    
    # Hämta och analysera thoughts
    thoughts = get_session_thoughts(session_id)
    analyze_session_thoughts(thoughts)
    
    # Hämta och analysera tool results
    tool_results = get_session_tool_results(session_id)
    analyze_tool_results(tool_results)
    
    print(f"\n✅ Felsökning klar för session {session_id}")

if __name__ == "__main__":
    main()
