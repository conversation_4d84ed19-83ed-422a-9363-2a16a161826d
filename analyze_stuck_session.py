#!/usr/bin/env python3
"""
Analysera den fastnade AI Agent sessionen
"""

import sys
import os
from datetime import datetime
sys.path.append('apps/backend-api')

def analyze_stuck_session():
    """Analysera den fastnade sessionen i detalj"""
    print('🔍 ANALYZING STUCK AI AGENT SESSION')
    print('=' * 50)
    
    try:
        # Use Docker database connection directly
        from sqlalchemy import create_engine, text
        from sqlalchemy.orm import sessionmaker
        
        docker_db_url = "postgresql://postgres:postgres@localhost:5432/aggie_dev"
        engine = create_engine(docker_db_url)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        
        db = SessionLocal()
        
        # Find running sessions
        running_query = text("""
            SELECT id, status, current_step_index, started_at, source_metadata, 
                   created_at, retry_count, max_retries, requires_human_review, action_item_reason
            FROM agent_sessions 
            WHERE status = 'RUNNING'
            ORDER BY started_at DESC
        """)
        
        running_sessions = db.execute(running_query).fetchall()
        
        if not running_sessions:
            print('✅ No stuck sessions found!')
            return
        
        print(f'🏃 Found {len(running_sessions)} running sessions:')
        
        for session in running_sessions:
            session_id = str(session[0])
            status = session[1]
            current_step = session[2]
            started_at = session[3]
            source_metadata = session[4]
            created_at = session[5]
            retry_count = session[6]
            max_retries = session[7]
            requires_review = session[8]
            action_reason = session[9]
            
            print(f'\n📋 SESSION: {session_id}')
            print(f'  Status: {status}')
            print(f'  Current Step: {current_step}')
            print(f'  Created: {created_at}')
            print(f'  Started: {started_at}')
            print(f'  Retry Count: {retry_count}/{max_retries}')
            print(f'  Requires Review: {requires_review}')
            
            # Calculate how long it's been running
            if started_at:
                elapsed = datetime.utcnow() - started_at.replace(tzinfo=None)
                print(f'  ⏰ Running for: {elapsed.total_seconds():.0f} seconds')
                
                if elapsed.total_seconds() > 300:  # 5 minutes
                    print('  ⚠️  SESSION IS STUCK (running > 5 minutes)')
            
            # Analyze source metadata
            if source_metadata:
                print(f'\n📄 SOURCE METADATA:')
                for key, value in source_metadata.items():
                    print(f'    {key}: {value}')
                
                # Check if file exists
                file_path = source_metadata.get('file_path')
                if file_path:
                    if os.path.exists(file_path):
                        file_size = os.path.getsize(file_path)
                        print(f'    ✅ File exists: {file_size} bytes')
                    else:
                        print(f'    ❌ File missing: {file_path}')
            
            # Get execution steps for this session
            steps_query = text("""
                SELECT step_name, status, step_index, started_at, completed_at, 
                       input_data, output_data
                FROM execution_steps 
                WHERE session_id = :session_id
                ORDER BY step_index
            """)
            
            steps = db.execute(steps_query, {"session_id": session_id}).fetchall()
            
            print(f'\n📊 EXECUTION STEPS ({len(steps)}):')
            for step in steps:
                step_name = step[0]
                step_status = step[1]
                step_index = step[2]
                step_started = step[3]
                step_completed = step[4]
                input_data = step[5]
                output_data = step[6]
                
                status_icon = {
                    'pending': '⏳',
                    'running': '🏃',
                    'completed': '✅',
                    'failed': '❌'
                }.get(step_status, '❓')
                
                print(f'  {status_icon} Step {step_index}: {step_name} ({step_status})')
                
                if step_started:
                    print(f'    Started: {step_started}')
                    if step_status == 'running':
                        elapsed = datetime.utcnow() - step_started.replace(tzinfo=None)
                        print(f'    ⏰ Running for: {elapsed.total_seconds():.0f} seconds')
                
                if step_completed:
                    print(f'    Completed: {step_completed}')
                
                # Show input/output for current step
                if step_index == current_step:
                    print(f'    📥 INPUT: {input_data}')
                    print(f'    📤 OUTPUT: {output_data}')
            
            # Get tool results
            tool_results_query = text("""
                SELECT tool_name, success, error_message, created_at, output_data
                FROM tool_results 
                WHERE session_id = :session_id
                ORDER BY created_at DESC
            """)
            
            tool_results = db.execute(tool_results_query, {"session_id": session_id}).fetchall()
            
            print(f'\n🔧 TOOL RESULTS ({len(tool_results)}):')
            for result in tool_results:
                tool_name = result[0]
                success = result[1]
                error_msg = result[2]
                created = result[3]
                output = result[4]
                
                status_icon = '✅' if success else '❌'
                print(f'  {status_icon} {tool_name} at {created}')
                
                if error_msg:
                    print(f'    ❌ Error: {error_msg}')
                
                if output and not success:
                    print(f'    📤 Output: {output}')
            
            # Get thought chain
            thoughts_query = text("""
                SELECT thought_type, content, sequence_number, created_at
                FROM thought_chains 
                WHERE session_id = :session_id
                ORDER BY sequence_number DESC
                LIMIT 5
            """)
            
            thoughts = db.execute(thoughts_query, {"session_id": session_id}).fetchall()
            
            print(f'\n🧠 RECENT THOUGHTS ({len(thoughts)}):')
            for thought in thoughts:
                thought_type = thought[0]
                content = thought[1]
                seq = thought[2]
                created = thought[3]
                
                print(f'  {seq}. {thought_type} at {created}')
                if isinstance(content, dict):
                    message = content.get('message', 'No message')
                    print(f'     {message}')
                    if 'error' in content:
                        print(f'     ❌ Error: {content["error"]}')
        
        db.close()
        
        # Recommendations
        print(f'\n💡 RECOMMENDATIONS:')
        print('1. Check if Celery worker is processing the task')
        print('2. Verify file exists and is accessible')
        print('3. Check for database connection issues in the worker')
        print('4. Consider cancelling stuck session if > 10 minutes')
        
    except Exception as e:
        print(f'❌ Analysis error: {e}')

def main():
    analyze_stuck_session()

if __name__ == "__main__":
    main()
