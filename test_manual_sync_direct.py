#!/usr/bin/env python3
"""
Test Manual Sync by calling the API directly with proper authentication
"""

import requests
import json
import time

# Configuration
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"

def get_auth_token():
    """Get authentication token by logging in"""
    try:
        # Try to login with demo credentials
        login_data = {
            "email": "<EMAIL>",
            "password": "admin123"
        }

        response = requests.post(f"{API_BASE}/auth/token", json=login_data)
        if response.status_code == 200:
            result = response.json()
            return result.get("access_token")
        else:
            print(f"❌ Login failed: {response.status_code}")
            print(f"Response: {response.text}")

            # Try alternative credentials
            login_data = {
                "email": "<EMAIL>",
                "password": "admin123"
            }

            response = requests.post(f"{API_BASE}/auth/token", json=login_data)
            if response.status_code == 200:
                result = response.json()
                return result.get("access_token")
            else:
                print(f"❌ Alternative login also failed: {response.status_code}")
                print(f"Response: {response.text}")
                return None

    except Exception as e:
        print(f"❌ Error during login: {e}")
        return None

def test_manual_sync_with_auth():
    """Test Manual Sync with proper authentication"""
    print("🔍 Testing Manual Sync with Authentication")
    print("=" * 50)
    
    # Get auth token
    token = get_auth_token()
    if not token:
        print("❌ Could not get authentication token")
        return
    
    print("✅ Got authentication token")
    
    # Set up headers
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json",
        "X-Tenant-ID": "94311644-a034-4967-87ea-bc46068ab6be"  # Demo Company tenant ID
    }
    
    try:
        # Test getting integrations first
        print("\n1. Getting integrations...")
        response = requests.get(f"{API_BASE}/integrations/", headers=headers)
        if response.status_code == 200:
            integrations = response.json()
            print(f"✅ Found {len(integrations)} integrations")
            
            for integration in integrations:
                print(f"  - {integration['integration_type']}: {integration['name']} (Active: {integration['is_active']})")
        else:
            print(f"❌ Failed to get integrations: {response.status_code}")
            print(f"Response: {response.text}")
            return
        
        # Test manual sync all
        print("\n2. Testing Manual Sync All...")
        response = requests.post(f"{API_BASE}/integrations/sync-all", headers=headers)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            results = response.json()
            print(f"✅ Manual Sync completed successfully!")
            print(f"Results: {json.dumps(results, indent=2)}")
        else:
            print(f"❌ Manual Sync failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error during manual sync test: {e}")

def test_individual_integration_sync():
    """Test syncing individual integration"""
    print("\n🔍 Testing Individual Integration Sync")
    print("=" * 45)
    
    # Get auth token
    token = get_auth_token()
    if not token:
        return
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json",
        "X-Tenant-ID": "94311644-a034-4967-87ea-bc46068ab6be"  # Demo Company tenant ID
    }
    
    try:
        # Get integrations
        response = requests.get(f"{API_BASE}/integrations/", headers=headers)
        if response.status_code == 200:
            integrations = response.json()
            
            if integrations:
                integration_id = integrations[0]['id']
                print(f"Testing sync for integration: {integrations[0]['name']}")
                
                # Test individual sync
                response = requests.post(f"{API_BASE}/integrations/{integration_id}/sync", headers=headers)
                
                print(f"Status Code: {response.status_code}")
                print(f"Response: {response.text}")
                
                if response.status_code == 200:
                    result = response.json()
                    print(f"✅ Individual sync completed!")
                    print(f"Result: {json.dumps(result, indent=2)}")
                else:
                    print(f"❌ Individual sync failed: {response.status_code}")
            else:
                print("No integrations found to test")
                
    except Exception as e:
        print(f"❌ Error during individual sync test: {e}")

def main():
    print("🚀 Direct Manual Sync API Test")
    print("=" * 50)
    
    # Test with authentication
    test_manual_sync_with_auth()
    
    # Test individual integration sync
    test_individual_integration_sync()
    
    print("\n" + "=" * 50)
    print("📋 SUMMARY:")
    print("This test calls the Manual Sync API directly with proper authentication")
    print("to see if the issue is in the frontend or backend.")

if __name__ == "__main__":
    main()
